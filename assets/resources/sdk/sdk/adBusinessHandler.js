const { SendNormalRequest } = require('./request');

let deviceId = '';
let channel = '';
let appid = '';
let uctrackid = '';

function reportAdValue(appid, adType, ecpm, channel, uctrackid) {
    SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_click?appid_key=${appid}&ad_type=${adType}&ecpm_value=${ecpm}&device_id=${deviceId}&channel=${channel}&uctrackid=${uctrackid}`)
        .then(() => { })
        .catch(() => { });
}

export function initDeviceId() {
    try {
        if (window['qg'].getSystemInfoSync().platformVersionCode >= 1096) {
            window['qg'].getDeviceId({
                success: function (data) {
                    console.log(`handling success: ${data.deviceId}`);
                    deviceId = data.deviceId;
                },
                fail: function (data, code) {
                    console.error(`handling fail, code = ${code}`);
                },
            });
        }
    } catch (error) {
        console.error('getDeviceId error', error);
    }
}

export function reportAdShow(res, type) {
    try {
        let ecpmPrice = 0;
        if (res !== null) {
            ecpmPrice = res.ECPM && !isNaN(res.ECPM.ECPMPrice) ? res.ECPM.ECPMPrice : 0;
        }
        if (channel === '') {
            channel = cc.sys.localStorage.getItem('channel') || 'no_channel';
        }
        if (appid === '') {
            appid = cc.sys.localStorage.getItem('appid') || 'default';
        }
        checkCallback(appid, ecpmPrice, channel, type);
    } catch (error) {
        console.error('reportAdShow error', error);
    }
}

/**
 * 处理广告ECPM并计算累计ARPU值
 * @param {object} res 广告返回结果对象，包含ECPM信息
 * @returns {boolean} 是否达到配置的ARPU阈值
 */
export function handleAdECPM(res, type) {
    let ecpmPrice = 0
    if (res !== null) {
        ecpmPrice = res.ECPM && !isNaN(res.ECPM.ECPMPrice) ? res.ECPM.ECPMPrice : 0;
    }
    if (channel === '') {
        channel = cc.sys.localStorage.getItem('channel') || 'no_channel';
    }
    if (appid === '') {
        appid = cc.sys.localStorage.getItem('appid') || 'default';
    }
    if (uctrackid === '') {
        uctrackid = cc.sys.localStorage.getItem('uctrackid') || '';
    }
    reportAdValue(appid, type, ecpmPrice, channel, uctrackid);
}

function checkCallback(appid, ecpmPrice, channel, type) {
    if (uctrackid === '') {
        uctrackid = cc.sys.localStorage.getItem('uctrackid') || '';
    }
    SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/new_shave?appid_key=${appid}&device_id=${deviceId}&uctrackid=${uctrackid}&ecpm_value=${ecpmPrice}&channel=${channel}&ad_type=${type}`)
        .then(() => { })
        .catch(() => { });
}
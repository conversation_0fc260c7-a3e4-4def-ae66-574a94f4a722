import { SendNormalRequest, SendRequest } from "./BusinessRequest";
import { handleAdECPM, initDeviceId, reportAdShow } from "./BusinessHandler";

const { ccclass, property } = cc._decorator;

@ccclass
export class FirstBusiness extends cc.Component {
    gridAdUnitId = "2235731";
    nativeAdUnitId = "2235729";
    rewardAdUnitId = "2512151";
    fullInsertUnitId = "2235734";
    halfInsertUnitId = "2235735";

    rewardAdUnitId2 = "2512152";
    rewardAdUnitId3 = "2512153";
    rewardAdUnitId4 = "2512154";

    isRewardShowed = false;
    isAdChannel = false;

    static instance = null;

    isSwitchEnableVideo = true;

    channelAdid = '';

    onLoad() {
        cc.game.addPersistRootNode(this.node);
    }

    static getIsRewardShowed() {
        return FirstBusiness.instance ? FirstBusiness.instance.isRewardShowed : false;
    }

    start() {
        initDeviceId();
        try {
            const enter = window['qg'].getEnterOptionsSync();
            var appid = cc.sys.localStorage.getItem('appid') || 'default';
            if (enter && enter.query && 'channel' in enter.query) {
                if (enter.query.channel !== '') {
                    if ('uctrackid' in enter.query) {
                        cc.sys.localStorage.setItem('uctrackid', enter.query.uctrackid);
                    }
                    if ('appid' in enter.query) {
                        appid = enter.query.appid;
                        cc.sys.localStorage.setItem('appid', enter.query.appid);
                    }
                    if ('adid' in enter.query && enter.query.adid && enter.query.adid !== 'false') {
                        cc.sys.localStorage.setItem('adid', enter.query.adid);
                    } else {
                        cc.sys.localStorage.setItem('adid', '');
                    }
                    if ('strategy' in enter.query && enter.query.strategy && enter.query.strategy !== 'false') {
                        cc.sys.localStorage.setItem('strategy', enter.query.strategy);
                        SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=mini-strategy-${enter.query.strategy}`).then(response => { }).catch(error => { });
                    } else {
                        cc.sys.localStorage.setItem('strategy', '0');
                    }

                    if ('configId' in enter.query) {
                        cc.sys.localStorage.setItem('configId', enter.query.configId);
                    } else {
                        SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=mini-no-configid`).then(response => {
                            console.log('请求成功', response);
                        }).catch(error => {
                            console.log('请求失败', error);
                        });
                    }
                }
                cc.sys.localStorage.setItem('channel', enter.query.channel);
                this.isAdChannel = true;
            } else if (cc.sys.localStorage.getItem('isAdChannel') == "1") {
                this.isAdChannel = true;
            } else {
                this.isAdChannel = false;
            }
        } catch (error) { }
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);

        cc.sys.localStorage.setItem('cold_start', "1");
        cc.sys.localStorage.setItem('shave_callback', "0");

        this.channelAdid = cc.sys.localStorage.getItem('adid') || '';
        if (this.channelAdid) {
            SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=${this.channelAdid}`).then(response => { }).catch(error => { });
        }

        if (cc.sys.localStorage.getItem('channel') == "uc") {
            cc.sys.localStorage.setItem('uc_callback', "0");
            if (cc.sys.localStorage.getItem('uc_report') == null) {
                cc.sys.localStorage.setItem('uc_report', "1");
                const uctrackid = cc.sys.localStorage.getItem('uctrackid') || '';
                const event_time = Date.now(); // 获取当前时间戳（毫秒）
                const callbackUrl = `https://huichuan.uc.cn/callback/appapi?uctrackid=${uctrackid}&type=44&event_time=${event_time}`;
                SendNormalRequest('GET', callbackUrl)
                    .then(responseText => { })
                    .catch(error => { });
                if (!uctrackid) {
                    SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=mini-no-uctrackid`).then(response => {
                        console.log('请求成功', response);
                    }).catch(error => {
                        console.log('请求失败', error);
                    });
                }
                SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=mini-enter-uv_new`)
                    .then(responseText => { })
                    .catch(error => { });
                if (!this.isBindingEnabled()) {
                    SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=no-binding`)
                        .then(responseText => { })
                        .catch(error => { });
                }
            }
        }

        if (this.isAdChannel) {
            if (this.channelAdid) {
                this.showVideoAd(this.channelAdid, () => {
                    this.showVideoAd(this.channelAdid, () => {
                        this.showNativeAd();
                        this.showVideoAd(this.channelAdid, () => {
                            this.showVideoAd(this.channelAdid, () => { });
                        });
                    });
                });
            } else {
                this.showVideoAd(this.rewardAdUnitId, () => {
                    this.showVideoAd(this.rewardAdUnitId2, () => {
                        this.showNativeAd();
                        this.showVideoAd(this.rewardAdUnitId3, () => {
                            this.showVideoAd(this.rewardAdUnitId4, () => { });
                        });
                    });
                });
            }
        }
    }

    onKeyDown(event) {
        console.log('event.keyCode:', event.keyCode);
        if (event.keyCode === cc.macro.KEY.back) {
            if (this.isAdChannel) {
                this.showBackVideoAd();
                this.showGridAd();
                this.showNativeAd();
            }
        }
    }

    showBackVideoAd() {
        // 四个视频广告随机 id
        const rewardIds = [this.rewardAdUnitId, this.rewardAdUnitId2, this.rewardAdUnitId3, this.rewardAdUnitId4];
        const randomIndex = Math.floor(Math.random() * rewardIds.length);
        const randomRewardId = rewardIds[randomIndex];
        this.showVideoAd(randomRewardId, () => {
            this.showBackVideoAd();
        });
    }

    showGridAd() {
        try {
            const platformVersionCode = window['qg'].getSystemInfoSync().platformVersionCode;
            if (platformVersionCode < 1076) {
                console.log("快应用平台版本号低于1076，暂不支持互推盒子相关 API");
                return;
            }

            var gridAd = window['qg'].createGamePortalAd({ adUnitId: this.gridAdUnitId });

            gridAd.offLoad();
            gridAd.load();
            gridAd.onLoad(() => {
                gridAd.show();
            });
        } catch (error) { }
    }

    showNativeAd() {
        console.log('showNativeAd');
        try {
            const { screenWidth, screenHeight } = window['qg'].getSystemInfoSync();
            var customAd = window['qg'].createCustomAd({
                adUnitId: this.nativeAdUnitId,
                style: {
                    width: screenWidth,
                },
            });
            var loadRes = null;
            customAd.offLoad();
            customAd.onLoad((res) => {
                console.log('customAd.onLoad', JSON.stringify(res));
            });
            customAd.offClick();
            customAd.onClick((clickRes) => {
                console.log('customAd.onClick', JSON.stringify(clickRes));
                handleAdECPM(loadRes, 'native');
            });
            if (this.isBindingEnabled()) {
                try {
                    customAd.offGetAdECPM();
                    customAd.offNotifyAdRankWin();

                    customAd.onGetAdECPM((ecpmRes) => {
                        console.log('customAd.onGetAdECPM', JSON.stringify(ecpmRes));
                        this.validateAdECPMResponse(ecpmRes, 'native');
                        loadRes = ecpmRes;
                        reportAdShow(loadRes, 'native');
                    });
                    customAd.getAdECPM();
                    customAd.onNotifyAdRankWin((res) => {
                        console.log('customAd.onNotifyAdRankWin', JSON.stringify(res));
                        this.validateNotifyAdRankWinResponse(res, 'native');
                    });
                    customAd.notifyAdRankWin({
                        lossPrice: 1000,
                    });
                } catch (error) {
                    console.log('customAd.getAdECPM error', JSON.stringify(error));
                    this.reportErrorEvent('customAd-binding-error');
                }
            }

            customAd.show();
        } catch (error) {
            console.log('showNativeAd error', error);
        }
    }

    showVideoAd(rewardId, onComplete) {
        console.log('showVideoBusinessAd');
        try {
            var videoAd = window['qg'].createRewardedVideoAd({ adUnitId: rewardId });
            var loadRes = null;
            const handleClose = (res) => {
                if (onComplete) onComplete();
                onComplete = () => { };
                this.isRewardShowed = false;
            };

            videoAd.offLoad();
            videoAd.offError();
            videoAd.offClose();
            videoAd.offClick();

            videoAd.load();
            videoAd.onLoad((res) => {
                if (this.isBindingEnabled()) {
                    try {
                        videoAd.offGetAdECPM();
                        videoAd.offNotifyAdRankWin();

                        videoAd.onGetAdECPM((ecpmRes) => {
                            console.log('videoAd.onGetAdECPM', JSON.stringify(ecpmRes));
                            this.validateAdECPMResponse(ecpmRes, 'video');
                            loadRes = ecpmRes;
                            reportAdShow(loadRes, 'video');
                        });
                        videoAd.getAdECPM();
                        videoAd.onNotifyAdRankWin((res) => {
                            console.log('videoAd.onNotifyAdRankWin', JSON.stringify(res));
                            this.validateNotifyAdRankWinResponse(res, 'video');
                        });
                        videoAd.notifyAdRankWin({
                            lossPrice: 1000,
                        });
                    } catch (error) {
                        console.log('videoAd.getAdECPM error', JSON.stringify(error));
                        this.reportErrorEvent('video-binding-error');
                        loadRes = res;
                        reportAdShow(loadRes, 'video');
                    }
                } else if (this.isSwitchEnableVideo) {
                    loadRes = res;
                    reportAdShow(loadRes, 'video');
                }
                this.isRewardShowed = true;
                videoAd.show();
            });
            videoAd.onError((err) => {
                this.showFullInsertAd(() => {
                    if (onComplete) onComplete();
                    onComplete = () => { };
                });
                this.isRewardShowed = false;
            });
            videoAd.onClick((clickRes) => {
                console.log('videoAd.onClick', JSON.stringify(clickRes));
                handleAdECPM(loadRes, 'video');
                loadRes = null;
            });
            videoAd.onClose(handleClose);
        } catch (error) {
            this.showFullInsertAd(() => {
                if (onComplete) onComplete();
                onComplete = () => { };
            });
            this.isRewardShowed = false;
        }
    }

    showFullInsertAd(onComplete, fallback = true, autoDestroy = false) {
        console.log('showFullInsertAd');
        try {
            var insertAd = window['qg'].createInterstitialAd({
                adUnitId: this.fullInsertUnitId
            });
            insertAd.offLoad();
            insertAd.offError();
            insertAd.offClose();
            insertAd.offClick();

            insertAd.load();
            var loadRes = null;
            insertAd.onLoad((res) => {
                if (this.isBindingEnabled()) {
                    try {
                        insertAd.offGetAdECPM();
                        insertAd.offNotifyAdRankWin();

                        insertAd.onGetAdECPM((ecpmRes) => {
                            console.log('fullInsertAd.onGetAdECPM', JSON.stringify(ecpmRes));
                            this.validateAdECPMResponse(ecpmRes, 'full_insert');
                            loadRes = ecpmRes;
                            reportAdShow(loadRes, 'full_insert');
                        });
                        insertAd.getAdECPM();
                        insertAd.onNotifyAdRankWin((res) => {
                            console.log('fullInsertAd.onNotifyAdRankWin', JSON.stringify(res));
                            this.validateNotifyAdRankWinResponse(res, 'full_insert');
                        });
                        insertAd.notifyAdRankWin({
                            lossPrice: 1000,
                        });
                    } catch (error) {
                        console.log('fullInsertAd.getAdECPM error', JSON.stringify(error));
                        this.reportErrorEvent('full-insert-binding-error');
                        loadRes = res;
                        reportAdShow(loadRes, 'full_insert');
                    }
                } else if (this.isSwitchEnableVideo) {
                    loadRes = res;
                    reportAdShow(loadRes, 'full_insert');
                }
                this.isRewardShowed = true;
                insertAd.show();
                if (autoDestroy) {
                    setTimeout(() => {
                        try {
                            if (insertAd) {
                                insertAd.destroy();
                            }
                        } catch (error) { }
                    }, 1000);
                }
            });
            insertAd.onError((error) => {
                if (fallback) {
                    this.showHalfInsertAd(onComplete);
                }
                this.isRewardShowed = false;
            });
            insertAd.onClose(() => {
                if (onComplete) onComplete();
                onComplete = () => { };
                this.isRewardShowed = false;
            });
            insertAd.onClick((clickRes) => {
                console.log('fullInsertAd.onClick', JSON.stringify(clickRes));
                handleAdECPM(loadRes, 'full_insert');
                loadRes = null;
            });
        } catch (error) {
            if (fallback) {
                this.showHalfInsertAd(onComplete);
            } else {
                if (onComplete) onComplete();
                onComplete = () => { };
            }
            this.isRewardShowed = false;
        }
    }

    showHalfInsertAd(onComplete) {
        console.log('showHalfInsertAd');
        try {
            var insertAd = window['qg'].createInterstitialAd({
                adUnitId: this.halfInsertUnitId
            });
            insertAd.offLoad();
            insertAd.offError();
            insertAd.offClose();
            insertAd.offClick();

            var loadRes = null;
            insertAd.load();
            insertAd.onLoad((res) => {
                if (this.isBindingEnabled()) {
                    try {
                        insertAd.offGetAdECPM();
                        insertAd.offNotifyAdRankWin();

                        insertAd.onGetAdECPM((ecpmRes) => {
                            console.log('halfInsertAd.onGetAdECPM', JSON.stringify(ecpmRes));
                            this.validateAdECPMResponse(ecpmRes, 'half_insert');
                            loadRes = ecpmRes;
                            reportAdShow(loadRes, 'half_insert');
                        });
                        insertAd.getAdECPM();
                        insertAd.onNotifyAdRankWin((res) => {
                            console.log('halfInsertAd.onNotifyAdRankWin', JSON.stringify(res));
                            this.validateNotifyAdRankWinResponse(res, 'half_insert');
                        });
                        insertAd.notifyAdRankWin({
                            lossPrice: 1000,
                        });
                    } catch (error) {
                        console.log('halfInsertAd.getAdECPM error', JSON.stringify(error));
                        this.reportErrorEvent('half-insert-binding-error');
                        loadRes = res;
                        reportAdShow(loadRes, 'half_insert');
                    }
                } else if (this.isSwitchEnableVideo) {
                    loadRes = res;
                    reportAdShow(loadRes, 'half_insert');
                }
                insertAd.show();
            });
            insertAd.onError((error) => {
                if (onComplete) onComplete();
                onComplete = () => { };
                this.showNativeAd();
            });
            insertAd.onClose(() => {
                if (onComplete) onComplete();
                onComplete = () => { };
            });
            insertAd.onClick((clickRes) => {
                console.log('halfInsertAd.onClick', JSON.stringify(clickRes));
                handleAdECPM(loadRes, 'half_insert');
                loadRes = null;
            });
        } catch (error) {
            if (onComplete) onComplete();
            onComplete = () => { };
            this.showNativeAd();
        }
    }

    isBindingEnabled() {
        try {
            return window['qg'].getSystemInfoSync().platformVersionCode >= 1160;
        } catch (error) {
            return false;
        }
    }

    reportErrorEvent(reportName) {
        var appid = cc.sys.localStorage.getItem('appid') || 'default';
        SendNormalRequest('GET', `https://stateless.unity.cn/release/1156179e-6f72-4b30-940a-8f7fca6efaa5/ad_report?appid_key=${appid}&report_name=${reportName}`)
            .then(responseText => { })
            .catch(error => { });
    }

    validateAdECPMResponse(res, adType) {
        try {
            if (!res || !('code' in res)) {
                this.reportErrorEvent(`${adType}-ecpm-format-error`);
                return;
            }
            if (res.code !== 0) {
                this.reportErrorEvent(`${adType}-ecpm-code-${res.code}`);
                return;
            }
            if (!res.ECPM || !('ECPMPrice' in res.ECPM)) {
                this.reportErrorEvent(`${adType}-no-ecpm`);
            }
        } catch (error) { }
    }

    validateNotifyAdRankWinResponse(res, adType) {
        try {
            if (!res || !('code' in res)) {
                this.reportErrorEvent(`${adType}-notify-format-error`);
                return;
            }
            if (res.code !== 0) {
                this.reportErrorEvent(`${adType}-notify-code-${res.code}`);
            }
        } catch (error) { }
    }
}